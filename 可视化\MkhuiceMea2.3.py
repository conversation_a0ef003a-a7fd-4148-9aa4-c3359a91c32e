
from okx_sdkV1 import get_kline_data, OKXClient

from MkKu import save_json,save_df_to_excel,get_local_kline_data,analyze_trend_changes,get_trade_decision_v2,calculate_trading_profit,plot_capital_curve_v1
from MKTrade import rolling_trade ,analyze_trading_performance,plot_trading_results

import time
import pandas as pd
import numpy as np
from typing import List, Tuple,Dict, Any
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from matplotlib.font_manager import FontProperties, findfont, FontManager

def setup_chinese_fonts():
    """配置中文字体"""
    plt.rcParams['font.sans-serif'] = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 
        'WenQuanYi Micro Hei', 'sans-serif'
    ]
    plt.rcParams['axes.unicode_minus'] = False

def plot_kline_with_trade_signals(df: pd.DataFrame,
                                ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                time_col: str = 'open_time',
                                trade_col: str = 'tradeS',
                                state_col: str = 'emaStas',
                                title: str = 'K线图与交易信号',
                                figsize: tuple = (16, 10)):
    
    # 设置中文字体
    setup_chinese_fonts()
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 数据准备
    df_plot = df.copy()
    
    # 重命名OHLC列
    rename_map = {
        ohlc_cols[0]: 'Open',
        ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low',
        ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    # 设置时间索引
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        # 如果没有时间列，使用虚拟时间索引
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    # 处理交易信号
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    # 创建交易信号标记
    addplot_list = []
    
    # 做多信号 (绿色向上三角)
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998  # 稍微低于最低价
        addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    # 做空信号 (红色向下三角)
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002  # 稍微高于最高价
        addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    # 绘制K线图
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        try:
            if addplot_list:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    addplot=addplot_list,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
            else:
                fig, axes = mpf.plot(
                    df_plot[['Open', 'High', 'Low', 'Close']], 
                    type='candle',
                    style=s,
                    figsize=figsize,
                    returnfig=True,
                    show_nontrading=False
                )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")
    
    # 获取主图轴
    ax = axes[0]
    
    # 设置标题和标签
    ax.set_title(title, fontsize=14, pad=20, fontweight='bold')
    ax.set_ylabel('价格', fontsize=12)
    
    # 创建图例
    legend_elements = []
    
    if not long_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                      markersize=10, label='做多信号 (1)', linestyle='None')
        )
    
    if not short_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                      markersize=10, label='做空信号 (-1)', linestyle='None')
        )
    
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    # 统计信息
    total_signals = len(long_signals) + len(short_signals)
    long_count = len(long_signals)
    short_count = len(short_signals)
    
    # 在图上添加统计信息
    stats_text = f"交易信号统计:\n做多: {long_count}次\n做空: {short_count}次\n总计: {total_signals}次"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 优化布局
    plt.tight_layout()
    
    # 显示图表
    try:
        mpf.show()
    except Exception as e:
        print(f"显示图表时出错: {e}")
    
    # 返回统计信息
    return {
        'total_signals': total_signals,
        'long_signals': long_count,
        'short_signals': short_signals,
        'long_signal_times': long_signals.index.tolist() if not long_signals.empty else [],
        'short_signal_times': short_signals.index.tolist() if not short_signals.empty else []
    }

###强度可视化


def plot_kline_with_trade_signalsV1(df: pd.DataFrame,
                                    emaTrades: List[float],
                                    ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                    time_col: str = 'open_time',
                                    trade_col: str = 'tradeS',
                                    state_col: str = 'emaStas',
                                    title: str = 'K线图与交易信号',
                                    figsize: tuple = (16, 10)):
    """
    绘制K线图并标记带强度变化的交易信号（原图+强度图）
    
    Args:
        df (pd.DataFrame): 包含K线和交易信号数据的DataFrame
        emaTrades (List[float]): 信号强度列表，范围[-1, 1]，0为白色，±1为黑色
        ohlc_cols (List[str]): OHLC四列的列名列表，默认['open', 'high', 'low', 'close']
        time_col (str): 时间列名，默认'open_time'
        trade_col (str): 交易信号列名，默认'tradeS'
        state_col (str): 状态列名，默认'emaStas'
        title (str): 图表标题
        figsize (tuple): 图表大小
    """
    
    # 设置中文字体
    setup_chinese_fonts()
    
    # 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 数据准备
    df_plot = df.copy()
    
    # 重命名OHLC列
    rename_map = {
        ohlc_cols[0]: 'Open',
        ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low',
        ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    # 设置时间索引
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        # 如果没有时间列，使用虚拟时间索引
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    # 处理交易信号
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    # 获取所有有信号的位置（按时间顺序）
    signal_positions = []
    for idx in df_plot.index:
        if df_plot.loc[idx, trade_col] in [1, -1]:
            signal_positions.append(idx)
    
    total_signals = len(signal_positions)
    
    # 验证emaTrades长度
    if len(emaTrades) != total_signals:
        print(f"警告: emaTrades长度({len(emaTrades)})与信号总数({total_signals})不匹配")
        print(f"信号位置: {[i for i, pos in enumerate(df_plot.index) if df_plot.loc[pos, trade_col] in [1, -1]]}")
        # 补齐或截断
        if len(emaTrades) < total_signals:
            emaTrades = emaTrades + [0.0] * (total_signals - len(emaTrades))
        else:
            emaTrades = emaTrades[:total_signals]
    
    # 创建强度到颜色的映射函数
    def intensity_to_color(intensity: float, signal_type: int) -> str:
        """
        根据强度和信号类型生成颜色（更精细的颜色映射）
        intensity: [-1, 1] 范围的强度值
        signal_type: 1为做多(绿色系), -1为做空(红色系)
        """
        # 确保强度在[-1, 1]范围内
        intensity = max(-1, min(1, intensity))
        
        # 计算颜色深浅
        abs_intensity = abs(intensity)
        
        if signal_type == 1:  # 做多信号 - 绿色系
            if intensity == 0:
                return 'white'
            elif intensity > 0:
                # 正强度：白色 → 浅绿 → 深绿
                if abs_intensity <= 0.2:
                    green = 1.0 - abs_intensity * 2  # 1.0 → 0.6
                    return (0.0, green, 0.2)
                elif abs_intensity <= 0.5:
                    green = 0.6 - (abs_intensity - 0.2) * 1.33  # 0.6 → 0.2
                    return (0.0, green, 0.2)
                elif abs_intensity <= 0.8:
                    green = 0.4 - (abs_intensity - 0.5) * 0.67  # 0.4 → 0.2
                    return (0.0, green, 0.2)
                else:  # > 0.8
                    green = 0.2 - (abs_intensity - 0.8) * 1.0  # 0.2 → 0.0
                    return (0.0, max(0.0, green), 0.0)
            else:
                # 负强度：使用相似的绿色系但略有不同
                if abs_intensity <= 0.2:
                    green = 1.0 - abs_intensity * 2
                    return (0.1, green, 0.3)
                elif abs_intensity <= 0.5:
                    green = 0.6 - (abs_intensity - 0.2) * 1.33
                    return (0.1, green, 0.3)
                elif abs_intensity <= 0.8:
                    green = 0.4 - (abs_intensity - 0.5) * 0.67
                    return (0.1, green, 0.3)
                else:
                    green = 0.2 - (abs_intensity - 0.8) * 1.0
                    return (0.1, max(0.0, green), 0.1)
        
        else:  # 做空信号 - 红色系
            if intensity == 0:
                return 'white'
            elif intensity > 0:
                # 正强度：白色 → 浅红 → 深红
                if abs_intensity <= 0.2:
                    red = 1.0 - abs_intensity * 2
                    return (red, 0.0, 0.2)
                elif abs_intensity <= 0.5:
                    red = 0.6 - (abs_intensity - 0.2) * 1.33
                    return (red, 0.0, 0.2)
                elif abs_intensity <= 0.8:
                    red = 0.4 - (abs_intensity - 0.5) * 0.67
                    return (red, 0.0, 0.2)
                else:
                    red = 0.2 - (abs_intensity - 0.8) * 1.0
                    return (max(0.0, red), 0.0, 0.0)
            else:
                # 负强度：使用相似的红色系但略有不同
                if abs_intensity <= 0.2:
                    red = 1.0 - abs_intensity * 2
                    return (red, 0.1, 0.3)
                elif abs_intensity <= 0.5:
                    red = 0.6 - (abs_intensity - 0.2) * 1.33
                    return (red, 0.1, 0.3)
                elif abs_intensity <= 0.8:
                    red = 0.4 - (abs_intensity - 0.5) * 0.67
                    return (red, 0.1, 0.3)
                else:
                    red = 0.2 - (abs_intensity - 0.8) * 1.0
                    return (max(0.0, red), 0.1, 0.1)
    
    # 创建两个独立的图表，而不是子图
    plt.figure(figsize=(figsize[0], figsize[1]*1.5))
    
    # ==================== 绘制原始图 ====================
    # 创建原始信号的addplot
    original_addplot_list = []
    
    # 做多信号 (绿色向上三角)
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998
        original_addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    # 做空信号 (红色向下三角)
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002
        original_addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    # 绘制原始K线图
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        if original_addplot_list:
            fig1, axes1 = mpf.plot(
                df_plot[['Open', 'High', 'Low', 'Close']], 
                type='candle',
                style=s,
                addplot=original_addplot_list,
                figsize=(figsize[0], figsize[1]//2),
                returnfig=True,
                show_nontrading=False,
                title=f"{title} - 原始信号"
            )
        else:
            fig1, axes1 = mpf.plot(
                df_plot[['Open', 'High', 'Low', 'Close']], 
                type='candle',
                style=s,
                figsize=(figsize[0], figsize[1]//2),
                returnfig=True,
                show_nontrading=False,
                title=f"{title} - 原始信号"
            )
    
    # 添加原始图的图例
    ax1 = axes1[0]
    legend_elements = []
    if not long_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green',
                      markersize=10, label='做多信号 (1)', linestyle='None')
        )
    if not short_signals.empty:
        legend_elements.append(
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red',
                      markersize=10, label='做空信号 (-1)', linestyle='None')
        )
    if legend_elements:
        ax1.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    # ==================== 绘制强度图 ====================
    # 创建强度信号的addplot
    intensity_addplot_list = []
    
    # 为每个信号创建带强度颜色的标记
    for signal_idx, pos in enumerate(signal_positions):
        if signal_idx >= len(emaTrades):
            print(f"警告: 信号位置 {signal_idx} 超出emaTrades范围")
            break
            
        signal_type = df_plot.loc[pos, trade_col]
        intensity = emaTrades[signal_idx]
        color = intensity_to_color(intensity, signal_type)
        
        # 创建单个信号的Series
        signal_marker = pd.Series(index=df_plot.index, dtype=float)
        
        if signal_type == 1:  # 做多
            signal_marker.loc[pos] = df_plot.loc[pos, 'Low'] * 0.998
            marker = '^'
        else:  # 做空
            signal_marker.loc[pos] = df_plot.loc[pos, 'High'] * 1.002
            marker = 'v'
        
        # 添加到addplot列表
        intensity_addplot_list.append(
            mpf.make_addplot(signal_marker, type='scatter', markersize=120,
                           marker=marker, color=color, alpha=0.9)
        )
    
    # 绘制强度K线图
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        if intensity_addplot_list:
            fig2, axes2 = mpf.plot(
                df_plot[['Open', 'High', 'Low', 'Close']], 
                type='candle',
                style=s,
                addplot=intensity_addplot_list,
                figsize=(figsize[0], figsize[1]//2),
                returnfig=True,
                show_nontrading=False,
                title=f"{title} - 强度变化信号"
            )
        else:
            fig2, axes2 = mpf.plot(
                df_plot[['Open', 'High', 'Low', 'Close']], 
                type='candle',
                style=s,
                figsize=(figsize[0], figsize[1]//2),
                returnfig=True,
                show_nontrading=False,
                title=f"{title} - 强度变化信号"
            )
    
    # 在强度图上添加强度数值标注
    ax2 = axes2[0]
    for signal_idx, pos in enumerate(signal_positions):
        if signal_idx >= len(emaTrades):
            break
            
        signal_type = df_plot.loc[pos, trade_col]
        intensity = emaTrades[signal_idx]
        
        if signal_type == 1:  # 做多
            y_pos = df_plot.loc[pos, 'Low'] * 0.998
            y_offset = 15
        else:  # 做空
            y_pos = df_plot.loc[pos, 'High'] * 1.002
            y_offset = -25
        
        # 添加强度标注
        ax2.annotate(f'{intensity:.3f}', (pos, y_pos), 
                    xytext=(0, y_offset), 
                    textcoords='offset points', ha='center', va='center',
                    fontsize=8, bbox=dict(boxstyle='round,pad=0.2', facecolor='yellow', alpha=0.7))
    
    # 创建强度图例
    _create_intensity_legend(ax2)
    
    # 设置图形布局
    fig1.tight_layout()
    fig2.tight_layout()
    
    # 显示图表
    plt.show()
    
    # 统计信息
    long_count = len(long_signals)
    short_count = len(short_signals)
    total_signals = len(signal_positions)  # 使用实际信号位置数量
    
    # 强度统计
    if emaTrades:
        avg_intensity = np.mean([abs(x) for x in emaTrades])
        max_intensity = max([abs(x) for x in emaTrades])
        min_intensity = min([abs(x) for x in emaTrades])
    else:
        avg_intensity = max_intensity = min_intensity = 0
    
    # 在强度图上添加统计信息
    stats_text = (f"交易信号统计:\n"
                 f"做多: {long_count}次\n"
                 f"做空: {short_count}次\n"
                 f"总计: {total_signals}次\n"
                 f"平均强度: {avg_intensity:.3f}\n"
                 f"最大强度: {max_intensity:.3f}\n"
                 f"最小强度: {min_intensity:.3f}")
    
    # 获取强度图的坐标轴来添加统计信息
    ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 返回统计信息
    return {
        'total_signals': total_signals,
        'long_signals': long_count,
        'short_signals': short_count,
        'intensity_stats': {
            'average': avg_intensity,
            'max': max_intensity,
            'min': min_intensity,
            'values': emaTrades[:total_signals]
        },
        'long_signal_times': long_signals.index.tolist() if not long_signals.empty else [],
        'short_signal_times': short_signals.index.tolist() if not short_signals.empty else []
    }


def _plot_candlestick(ax, df_plot):
    """
    在指定的轴上绘制K线图
    """
    # 绘制K线
    for idx in df_plot.index:
        open_price = df_plot.loc[idx, 'Open']
        high_price = df_plot.loc[idx, 'High']
        low_price = df_plot.loc[idx, 'Low']
        close_price = df_plot.loc[idx, 'Close']
        
        # K线颜色
        color = 'green' if close_price >= open_price else 'red'
        
        # 绘制影线
        ax.plot([idx, idx], [low_price, high_price], color='black', linewidth=1)
        
        # 绘制实体
        body_height = abs(close_price - open_price)
        bottom = min(open_price, close_price)
        
        rect = plt.Rectangle((idx, bottom), width=pd.Timedelta(hours=12), height=body_height,
                           facecolor=color, alpha=0.7, edgecolor='black', linewidth=0.5)
        ax.add_patch(rect)


def _create_intensity_legend(ax):
    """
    创建详细的强度变化图例
    """
    legend_elements = []
    
    # 做多信号强度示例 (绿色系)
    legend_elements.extend([
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='white',
                  markersize=10, label='做多强度=0.0', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor=(0, 0.8, 0.2),
                  markersize=10, label='做多强度=0.2', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor=(0, 0.6, 0.2),
                  markersize=10, label='做多强度=0.5', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor=(0, 0.4, 0.2),
                  markersize=10, label='做多强度=0.8', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='^', color='w', markerfacecolor=(0, 0.2, 0),
                  markersize=10, label='做多强度=1.0', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
    ])
    
    # 分隔线
    legend_elements.append(
        plt.Line2D([0], [0], color='none', label='────────────', linestyle='None')
    )
    
    # 做空信号强度示例 (红色系)
    legend_elements.extend([
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='white',
                  markersize=10, label='做空强度=0.0', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor=(0.8, 0, 0.2),
                  markersize=10, label='做空强度=0.2', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor=(0.6, 0, 0.2),
                  markersize=10, label='做空强度=0.5', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor=(0.4, 0, 0.2),
                  markersize=10, label='做空强度=0.8', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
        plt.Line2D([0], [0], marker='v', color='w', markerfacecolor=(0.2, 0, 0),
                  markersize=10, label='做空强度=1.0', linestyle='None', 
                  markeredgecolor='black', markeredgewidth=1),
    ])
    
    # 负强度说明
    legend_elements.append(
        plt.Line2D([0], [0], color='none', label='注：负强度使用相似色调', linestyle='None')
    )
    
    # 创建图例，使用更好的布局
    legend = ax.legend(handles=legend_elements, loc='center left', 
                      bbox_to_anchor=(1.02, 0.5), fontsize=9, 
                      title='信号强度图例', title_fontsize=10,
                      frameon=True, fancybox=True, shadow=True)





def analyze_ema_state(
    ema_values: List[float] | Tuple[float, float, float],
    tolerance_percent: float = 0.005
) -> str:
    """
   
    """
    # 检查输入是否合法
    if not isinstance(ema_values, (list, tuple)) or len(ema_values) != 3:
        raise ValueError("输入参数 'ema_values' 必须是一个包含3个数字的列表或元组。")
    
    # 检查值是否为NaN
    if any(np.isnan(v) for v in ema_values):
        return "数据不足/无法计算"

    a, b, c = ema_values

    # --- 第一步：判定 A 和 B 的关系 ---
    # 防止 B 为 0 导致除法错误
    if b != 0 and abs(a - b) / abs(b) <= tolerance_percent:
        ab_state = "approx"  # A ≈ B
    elif a > b:
        ab_state = "greater"  # A > B
    else:
        ab_state = "less"  # A < B

    # --- 第二步：判定 B 和 C 的关系 ---
    # 防止 C 为 0 导致除法错误
    if c != 0 and abs(b - c) / abs(c) <= tolerance_percent:
        bc_state = "approx"  # B ≈ C
    elif b > c:
        bc_state = "greater"  # B > C
    else:
        bc_state = "less"  # B < C
        
    # --- 第三步：根据两个关系组合，从9宫格中匹配结果 ---
    
    # 规则 1-3: 短期动能强于中期 (A > B)
    if ab_state == "greater":
        if bc_state == "greater":   # A > B and B > C
            return "1. 强势多头 (A > B > C)"
        elif bc_state == "less":    # A > B and B < C
            return "2. 下跌中反弹"
        else:  # bc_state == "approx"  # A > B and B ≈ C
            return "3. 盘整后突破"

    # 规则 4-6: 短期动能弱于中期 (A < B)
    elif ab_state == "less":
        if bc_state == "greater":   # A < B and B > C
            return "4. 上涨中回调"
        elif bc_state == "less":    # A < B and B < C
            return "5. 强势空头 (C > B > A)"
        else:  # bc_state == "approx"  # A < B and B ≈ C
            return "6. 盘整后破位"
            
    # 规则 7-9: 短中期动能纠缠 (A ≈ B)
    else:  # ab_state == "approx"
        if bc_state == "greater":   # A ≈ B and B > C
            return "7. 上涨趋势减弱"
        elif bc_state == "less":    # A ≈ B and B < C
            return "8. 下跌趋势减弱"
        else:  # bc_state == "approx"  # A ≈ B and B ≈ C
            return "9. 极限盘整/无趋势"
def calculate_ema(data, period):
    """
    计算指数移动平均值 (EMA) - 优化版本
    
    参数:
        data: pandas Series 或 list，输入数据列
        period: int，EMA周期
    
    返回:
        pandas Series，EMA值序列，长度与输入数据相同
    """
    # 转换为pandas Series以便处理
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    data_length = len(data)
    
    # 长度不足，返回相同长度的0
    if data_length < period:
        return pd.Series(np.zeros(data_length), index=data.index)
    
    # 核心优化：转换为numpy数组，避免pandas索引开销
    values = data.values.astype(np.float64, copy=False)
    valid_mask = ~np.isnan(values)
    
    if np.sum(valid_mask) < period:
        return pd.Series(np.zeros(data_length), index=data.index)
    
    # 预分配numpy数组，比pandas Series快
    ema_values = np.zeros(data_length, dtype=np.float64)
    
    # 计算初始EMA
    valid_indices = np.where(valid_mask)[0]
    start_idx = period - 1
    ema_values[start_idx] = np.mean(values[valid_indices[:period]])
    
    # 预计算常数，避免重复计算
    alpha = 2.0 / (period + 1)
    one_minus_alpha = 1.0 - alpha
    
    # 优化的递推计算：减少条件判断和函数调用
    prev_ema = ema_values[start_idx]
    for i in range(start_idx + 1, data_length):
        if valid_mask[i]:  # 使用预计算的mask
            prev_ema = alpha * values[i] + one_minus_alpha * prev_ema
        ema_values[i] = prev_ema
    
    # 转换回pandas Series保持接口一致
    return pd.Series(ema_values, index=data.index)


def calculate_ema_optimized(df, column, period, N=3):
 
    df_length = len(df)
    threshold = N * period
    
    # 情况1：df长度小于period，数据不足
    if df_length < period:
        # print(f"数据长度({df_length})小于EMA周期({period})，返回全0序列")
        return pd.Series([0.0] * df_length, index=df.index)
    
    # 情况2：df长度在[period, N*period]之间，使用全部数据
    elif df_length <= threshold:
        # print(f"数据长度({df_length})在合理范围内，使用全部数据计算EMA{period}")
        slice_df = df
    
    # 情况3：df长度大于N*period，进行切片优化
    else:
        # print(f"数据长度({df_length})大于阈值({threshold})，切片使用最后{threshold}个数据点")
        slice_df = df.iloc[-threshold:]
    
    # 计算EMA
    ema_result = calculate_ema(slice_df[column], period)
    
    # 如果进行了切片，需要将结果扩展到原始DataFrame的长度
    if len(slice_df) < df_length:
        # 创建与原始DataFrame同长度的结果序列
        full_result = pd.Series([0.0] * df_length, index=df.index)
        # 将切片计算的结果放到对应位置
        full_result.iloc[-len(slice_df):] = ema_result.values
        return full_result
    
    return ema_result


def check_and_setup_chinese_fonts():
    """
    检查并设置中文字体，避免字符缺失警告
    """
    # 抑制字体相关警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    
    # 检查可用的中文字体
    fm = FontManager()
    chinese_fonts = []
    
    # 常见中文字体列表
    font_candidates = [
        'SimHei',           # 黑体
        'Microsoft YaHei',  # 微软雅黑
        'SimSun',          # 宋体
        'KaiTi',           # 楷体
        'Arial Unicode MS', # Mac
        'PingFang SC',     # Mac
        'Hiragino Sans GB', # Mac
        'WenQuanYi Micro Hei', # Linux
        'Noto Sans CJK SC', # Google
        'Source Han Sans SC', # Adobe
    ]
    
    # 查找可用的中文字体
    for font_name in font_candidates:
        try:
            font_path = findfont(FontProperties(family=font_name))
            if font_path and font_name.lower() not in font_path.lower().replace('DejaVu', '').lower():
                chinese_fonts.append(font_name)
        except:
            continue
    
    # 如果没有找到中文字体，尝试手动指定路径
    if not chinese_fonts:
        manual_font_paths = [
            'C:/Windows/Fonts/simhei.ttf',     # Windows 黑体
            'C:/Windows/Fonts/msyh.ttc',       # Windows 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',     # Windows 宋体
            '/System/Library/Fonts/Arial Unicode.ttc',  # Mac
            '/System/Library/Fonts/PingFang.ttc',       # Mac
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc', # Linux
        ]
        
        for font_path in manual_font_paths:
            try:
                import os
                if os.path.exists(font_path):
                    # 从路径提取字体名称
                    font_name = os.path.basename(font_path).split('.')[0]
                    chinese_fonts.append(font_name)
                    break
            except:
                continue
    
    # 设置字体优先级列表
    if chinese_fonts:
        font_list = chinese_fonts + ['sans-serif']
        print(f"找到中文字体: {chinese_fonts}")
    else:
        # 如果实在找不到，使用fallback方案
        font_list = ['sans-serif']
        print("警告: 未找到中文字体，将使用系统默认字体")
    
    # 全局设置字体
    plt.rcParams['font.sans-serif'] = font_list
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建字体属性对象
    try:
        if chinese_fonts:
            chinese_font = FontProperties(family=chinese_fonts[0], size=10)
        else:
            chinese_font = FontProperties(size=10)
    except:
        chinese_font = FontProperties(size=10)
    
    return chinese_font

def plot_states_with_fixed_fonts(df: pd.DataFrame,
                                ohlc_cols: list = ['open', 'high', 'low', 'close'],
                                state_col: str = 'emaStas',
                                title: str = '市场状态分析'):
    """
    修复了所有字体和警告问题的绘图函数
    """
    
    # 1. 设置中文字体
    chinese_font = check_and_setup_chinese_fonts()
    
    # 2. 数据验证
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    missing_cols = [col for col in ohlc_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的OHLC列: {missing_cols}")
    
    if state_col not in df.columns:
        raise ValueError(f"状态列 '{state_col}' 不存在于DataFrame中")
    
    # 3. 数据准备
    df_plot = df.copy()
    rename_map = {
        ohlc_cols[0]: 'Open', ohlc_cols[1]: 'High',
        ohlc_cols[2]: 'Low', ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    dummy_index = pd.to_datetime(pd.date_range(start='2000-01-01', periods=len(df_plot)))
    df_plot.set_index(dummy_index, inplace=True)

    # 4. 状态映射
    state_map = {
        0: {'desc': '0. 无效/初始状态', 'color': (0.0, 0.0, 0.0, 0.0)},
        1: {'desc': '1. 强势多头', 'color': (0.0, 1.0, 0.0, 0.3)},
        2: {'desc': '2. 下跌中反弹', 'color': (1.0, 0.647, 0.0, 0.3)},
        3: {'desc': '3. 盘整后突破', 'color': (0.678, 0.847, 0.902, 0.4)},
        4: {'desc': '4. 上涨中回调', 'color': (0.0, 0.392, 0.0, 0.3)},
        5: {'desc': '5. 强势空头', 'color': (1.0, 0.0, 0.0, 0.3)},
        6: {'desc': '6. 盘整后破位', 'color': (1.0, 0.753, 0.796, 0.4)},
        7: {'desc': '7. 上涨趋势减弱', 'color': (1.0, 1.0, 0.0, 0.3)},
        8: {'desc': '8. 下跌趋势减弱', 'color': (0.502, 0.0, 0.502, 0.3)},
        9: {'desc': '9. 极限盘整/无趋势', 'color': (0.502, 0.502, 0.502, 0.3)}
    }

    # 5. 绘制K线图（修复警告）
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    # 抑制mplfinance的弃用警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", DeprecationWarning)
        warnings.simplefilter("ignore", UserWarning)
        
        try:
            fig, axes = mpf.plot(
                df_plot, 
                type='candle', 
                style=s,
                ylabel='Price', 
                figsize=(16, 10),
                returnfig=True, 
                xrotation=0, 
                show_nontrading=False  # 新参数，替代no_xgaps
            )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")

    # 6. 状态数据处理
    def extract_state_number(state_str):
        if pd.isna(state_str):
            return None
        
        state_str = str(state_str).strip()
        if state_str.isdigit():
            return int(state_str)
        
        import re
        match = re.match(r'^(\d+)', state_str)
        if match:
            return int(match.group(1))
        return None
    
    processed_states = []
    for i, raw_state in enumerate(df_plot[state_col]):
        try:
            state = extract_state_number(raw_state)
            if state is not None and 0 <= state <= 9:
                processed_states.append(state)
            else:
                processed_states.append(None)
        except:
            processed_states.append(None)

    # 7. 绘制背景着色
    ax = axes[0]
    current_state = -1
    start_idx = 0
    
    for i, state in enumerate(processed_states):
        if state is None:
            continue
        
        if state != current_state:
            if current_state not in [-1, 0, None]:
                try:
                    ax.axvspan(start_idx - 0.5, i - 0.5,
                               color=state_map[current_state]['color'],
                               zorder=0)
                except:
                    pass
            current_state = state
            start_idx = i

    if current_state not in [-1, 0, None]:
        try:
            ax.axvspan(start_idx - 0.5, len(df_plot) - 0.5,
                       color=state_map[current_state]['color'],
                       zorder=0)
        except:
            pass

    # 8. 创建图例
    used_states = set(s for s in processed_states if s is not None and s != 0)
    if used_states:
        legend_patches = [
            mpatches.Patch(color=state_map[k]['color'], label=state_map[k]['desc'])
            for k in sorted(used_states) if k in state_map
        ]
        
        if legend_patches:
            fig.legend(handles=legend_patches, 
                      loc='upper left', 
                      fontsize=9, 
                      title='市场状态',
                      prop=chinese_font)

    # 9. 设置标题和标签
    ax.set_title(title, fontproperties=chinese_font, fontsize=14, pad=20, fontweight='bold')
    ax.set_xlabel('K线序列号', fontproperties=chinese_font, fontsize=11)
    ax.set_ylabel('价格', fontproperties=chinese_font, fontsize=11)
    
    # 10. 格式化坐标轴
    ax.xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}'))
    
    # 11. 设置刻度标签字体
    for label in ax.get_xticklabels() + ax.get_yticklabels():
        label.set_fontproperties(chinese_font)

    # 12. 优化布局并显示
    plt.tight_layout()
    
    # 抑制显示时的警告
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        mpf.show()
       

def analyze_kline_performance(df: pd.DataFrame, open_direction: int) -> tuple[list[str], pd.Series]:
    if df.empty:
        raise ValueError("输入的DataFrame不能为空。")
    if not all(col in df.columns for col in ['open', 'high', 'low', 'close']):
        raise ValueError("DataFrame 必须包含 'open', 'high', 'low', 'close' 列。")
    if open_direction not in [1, -1]:
        raise ValueError("open_direction 必须是 1 (开多) 或 -1 (开空)。")

    # 根据你提供的代码，这里是使用第二根K线的开盘价作为计算最大涨幅/回撤的基准
    # 如果你希望使用第一根K线的开盘价 (df.iloc[0]['open']) 或收盘价 (df.iloc[0]['close'])
    # 作为基准，请相应地修改这一行。
    if len(df) < 2:
        raise ValueError("DataFrame 至少需要包含 2 行数据才能使用 df.iloc[1]['open'] 作为基准。")
    
    # 确保用于性能计算的初始价格不为0
    initial_price_for_performance = df.iloc[0]['open']
    if initial_price_for_performance == 0:
        raise ValueError("计算最大涨幅和回撤的初始价格不能为0。")

    # 获取整个 DataFrame 的最高价和最低价
    max_price_overall = df['high'].max()
    min_price_overall = df['low'].min()

    max_good_value = 0.0 # 实际浮点数值
    max_bad_value = 0.0  # 实际浮点数值

    ## 计算整体最大涨幅 (max_good) 和最大回撤 (max_bad)
    if open_direction == 1: # 开多
        # max_good: 从 initial_price_for_performance 到整体最高价的涨幅
        max_good_value = (max_price_overall / initial_price_for_performance) - 1
        # max_bad: 从 initial_price_for_performance 到整体最低价的回撤
        max_bad_value = (min_price_overall / initial_price_for_performance) - 1
    elif open_direction == -1: # 开空
        # max_good (盈利): 从 initial_price_for_performance 到整体最低价的跌幅
        # 注意：如果 min_price_overall 为0，会导致除零。
        if min_price_overall == 0:
            max_good_value = np.inf # 无限盈利
        else:
            max_good_value = (initial_price_for_performance / min_price_overall) - 1

        # max_bad (亏损): 从 initial_price_for_performance 到整体最高价的涨幅
        # 注意：如果 max_price_overall 为0，会导致除零。
        if max_price_overall == 0:
            max_bad_value = -np.inf # 无限亏损
        else:
            max_bad_value = (initial_price_for_performance / max_price_overall) - 1

    ## 修复后的每根 K 线涨跌幅计算
    # 增加对 open 为0的保护，避免 ZeroDivisionError
    # 将 open 为 0 的替换为 NaN，以便在计算百分比变化时得到 NaN
    open_safe = df['open'].replace(0, np)
    
    # 正确计算从开盘价到收盘价的百分比变化: (close - open) / open
    percentage_change = (df['close'] - df['open']) / open_safe
    
    # 格式化为百分比字符串。对于 NaN 值，显示为 'NaN%'
    percentage_formatted = percentage_change.apply(lambda x: f"{x:.3%}" if pd.notna(x) else "NaN%")
    
    # 格式化单个数值 max_good_value 和 max_bad_value 为百分比字符串
    # 确保对 NaN, inf, -inf 值进行妥善处理，避免格式化错误
    formatted_max_good = f"{max_good_value:.3%}" if pd.notna(max_good_value) and np.isfinite(max_good_value) else f"{max_good_value}"
    formatted_max_bad = f"{max_bad_value:.3%}" if pd.notna(max_bad_value) and np.isfinite(max_bad_value) else f"{max_bad_value}"

    # 返回格式化后的 max_good 和 max_bad，以及逐 K 线的百分比 Series
    return [ open_direction,formatted_max_good, formatted_max_bad], percentage_formatted
        
def percentage_string(pct_string: str) -> float:

    if not isinstance(pct_string, str):
        raise TypeError("输入必须是字符串格式的百分比。")

    clean_str = pct_string.strip().replace('%', '')
    try:
        return float(clean_str) / 100.0
    except ValueError:
        raise ValueError(f"无法将 '{pct_string}' 解析为有效的百分比数值。")
def calculate_trading_signals(slice_df, price_columns, price_columns_reversed, periods, tolerance_percent, 
                             buy_nowSigle, sell_nowSigle, current_position, trade_direction):
    """
    计算买入和卖出交易信号
    
    Returns:
        tuple: (trade_signal_buy, reason_buy, state_for_buy, LivemaS_buy, 
                trade_signal_sell, reason_sell, state_for_sell, LivemaS_sell, 
                new_buy_nowSigle, new_sell_nowSigle)
    """
    # 计算买入信号
    LivemaS_buy = []
    for i in [0, 1, 2]:
        results_buy = calculate_ema_optimized(slice_df, price_columns[i], periods[i], N=3)
        LivemaS_buy.append(results_buy.iloc[-1])
    
    state_for_buy = analyze_ema_state(LivemaS_buy, tolerance_percent=tolerance_percent)
    temp_buy_lastSigle = buy_nowSigle
    new_buy_nowSigle = int(state_for_buy[:1])
    trade_signal_buy, reason_buy = get_trade_decision_v2(temp_buy_lastSigle, new_buy_nowSigle, 
                                                        current_position=current_position, 
                                                        trade_direction=trade_direction)
    
    # 计算卖出信号
    LivemaS_sell = []
    for i in [0, 1, 2]:
        results_sell = calculate_ema_optimized(slice_df, price_columns_reversed[i], periods[i], N=3)
        LivemaS_sell.append(results_sell.iloc[-1])
    
    state_for_sell = analyze_ema_state(LivemaS_sell, tolerance_percent=tolerance_percent)
    temp_sell_lastSigle = sell_nowSigle
    new_sell_nowSigle = int(state_for_sell[:1])
    trade_signal_sell, reason_sell = get_trade_decision_v2(temp_sell_lastSigle, new_sell_nowSigle, 
                                                          current_position=current_position, 
                                                          trade_direction=trade_direction)
    
    return (trade_signal_buy, reason_buy, state_for_buy, LivemaS_buy,
            trade_signal_sell, reason_sell, state_for_sell, LivemaS_sell,
            new_buy_nowSigle, new_sell_nowSigle)


def process_trade_signal(trade_signal_buy, trade_signal_sell, reason_buy, reason_sell, 
                        slice_df, any_df, state_for_buy, state_for_sell, 
                        start_idx, count, singleIndex, lastTradeSigle, buysellHistory, df, end_idx):
    """
    处理交易信号的最终决策逻辑
    
    Returns:
        tuple: (current_trade_signal, current_trade_price, current_ema_state, 
                good_bad, new_singleIndex, new_lastTradeSigle)
    """
    current_trade_signal = 0
    current_trade_price = 0
    current_ema_state = 0
    good_bad = 0
    new_singleIndex = singleIndex
    new_lastTradeSigle = lastTradeSigle

    if trade_signal_buy == 'BUY':
        # 优先处理 BUY 信号
        current_trade_signal = 1
        current_trade_price = slice_df.iloc[-1]['close']
        current_ema_state = state_for_buy
        
        good_bad, df_zf = analyze_kline_performance(any_df, 1)
        
        # 更新交易历史
        new_singleIndex, new_lastTradeSigle = update_trade_history(
            singleIndex, end_idx, lastTradeSigle, 1, buysellHistory, df
        )
        # print(slice_df[-4:])
        print(f"第{start_idx + count}根：BUY {reason_buy}  {current_trade_price} {good_bad}")
        
        if trade_signal_sell == 'SHORT':
            print(f"第📊{start_idx + count}根发生冲突：BUY {reason_buy} 和 SHORT {reason_sell}  {current_trade_price} ")
            
    elif trade_signal_sell == 'SHORT':
        current_trade_signal = -1
        current_trade_price = slice_df.iloc[-1]['close']
        current_ema_state = state_for_sell
        
        good_bad, df_zf = analyze_kline_performance(any_df, -1)
        
        # 更新交易历史
        new_singleIndex, new_lastTradeSigle = update_trade_history(
            singleIndex, end_idx, lastTradeSigle, -1, buysellHistory, df
        )
        
        print(f"第{start_idx + count}根：SHORT {reason_sell}  {current_trade_price} {good_bad}")
    
    return (current_trade_signal, current_trade_price, current_ema_state, 
            good_bad, new_singleIndex, new_lastTradeSigle)


def update_trade_history(singleIndex, end_idx, lastTradeSigle, current_signal, buysellHistory, df):
    """
    更新交易历史记录
    
    Returns:
        tuple: (new_singleIndex, new_lastTradeSigle)
    """
    if singleIndex == 0:
        return end_idx, current_signal
    else:
        buysellHistory[0].append(lastTradeSigle)
        buysellHistory[1].append([df.loc[singleIndex, 'open'].tolist()] + 
                                df.loc[singleIndex:end_idx-1, 'high'].tolist())
        buysellHistory[2].append([df.loc[singleIndex, 'open'].tolist()] + 
                                df.loc[singleIndex:end_idx-1, 'low'].tolist())
        return end_idx, current_signal


def update_result_lists(emaStasBuy, emaStasSell, LivemaS_buy, LivemaS_sell, 
                       emaTrades, state_for_buy, state_for_sell, 
                       tradeS, current_trade_signal, tradePrice, current_trade_price, 
                       emaStas, current_ema_state, any_good_bad, good_bad):
    """
    更新所有结果列表
    """
    emaStasBuy.append(f'{LivemaS_buy[0]:.5f}-{LivemaS_buy[1]:.5f}-{LivemaS_buy[2]:.5f}')
    emaStasSell.append(f'{LivemaS_sell[0]:.5f}-{LivemaS_sell[1]:.5f}-{LivemaS_sell[2]:.5f}')
    
    emaTrades[0].append(state_for_buy)
    emaTrades[1].append(state_for_sell)
    tradeS.append(current_trade_signal)
    tradePrice.append(current_trade_price)
    emaStas.append(current_ema_state)
    any_good_bad.append(good_bad)
    
if __name__ == "__main__":
     
    Uname='doge'
    Ktime='3m'
    strTime='2025-4-1'
    endTime='2025-6-10'
    zhiyin=0.01
    zhisun=0.03
    # print(strTime)
    try:
        # df = get_local_kline_data('doge', '15m', '2024-12-30 10:00:00','2025-2-1')
        df = get_local_kline_data(Uname, Ktime, strTime, endTime)
        print(df.head())
    except Exception as e:
        print(e)
        
    print(f"分析数据量: {len(df)} 条K线")
    print(f"数据时间范围: {df['open_time'].iloc[0]} 到 {df['open_time'].iloc[-1]}")
    

    count=100
    max_start_index = len(df) - count
    buy_lastSigle = 0
    buy_nowSigle = 0
    # 独立管理卖出信号的状态
    sell_lastSigle = 0
    sell_nowSigle = 0

    current_position = 'flat'  ##当前持仓状态 ('flat', 'long', 'short')。
    trade_direction = 'both'  ###交易方向控制 ('both', 'long_only', 'short_only')。
    tradeS = [] ##记录交易信号
    chicangPosition=0 ##记录当前持仓状态
    tradePrice = [] ##记录交易价格
    emaStas = [] # 确保在循环内每次迭代只添加一次最终状态
    emaTrades=[[],[]] ##记录ema状态
    emaStasBuy=[] ##分别记录buy仓位eam状态
    emaStasSell=[] ##分别记录sell仓位eam状态

    ####记录产生交易信号后的最低价格走势和最低价格走势
    # buyHistory=[[],[]]
    buysellHistory=[[],[],[]] ##
    singleIndex=0
    lastTradeSigle=0
    
    any_good_bad=[] ##统计最大回撤和涨幅
    goodBads=24
    # 根据用户提供的模板设置参数
    periods=[8, 16, 32]
    tolerance_percent=0.0015 ## analyze_ema_state灵敏度
    price_columns=['low', 'close', 'high'] # 根据用户提供的模板设置
    # price_columns=['close', 'close', 'close'] # 根据用户提供的模板设置
    price_columns_reversed = price_columns[::-1] # 预先计算反转后的价格列
   
    # 在循环外创建统计列表
    trade_stats = [[], [], [], []]  # [开仓方向, 开仓价, 平仓价, 涨跌幅]
    
    for start_idx in range(max_start_index + 1):
        # 计算结束索引和创建切片
        end_idx = start_idx + count
        slice_df = df.iloc[start_idx:end_idx]
        
        # 创建分析用的切片
        any_df = df.iloc[end_idx:end_idx+goodBads]
        if end_idx == len(df) - goodBads:
            break
        kHigh=slice_df.iloc[-1]['high']
        Klow=slice_df.iloc[-1]['low']
        # kHigh=slice_df.iloc[-1]['low']
        # Klow=slice_df.iloc[-1]['high']
        kClose=slice_df.iloc[-1]['close']
        KtimeSliceDf=slice_df.iloc[-1]['open_time']
        
        # 第一步：计算交易信号
        # 基于当前数据切片、价格列、周期参数等计算买入和卖出信号
        # 返回买入信号、买入原因、买入状态、实时移动平均线状态等信息
        (trade_signal_buy, reason_buy, state_for_buy, LivemaS_buy,
            trade_signal_sell, reason_sell, state_for_sell, LivemaS_sell,
            buy_nowSigle, sell_nowSigle) = calculate_trading_signals(
            slice_df, price_columns, price_columns_reversed, periods, tolerance_percent,
            buy_nowSigle, sell_nowSigle, current_position, trade_direction
        )

        # 第二步：处理交易信号
        # 根据计算出的买卖信号，结合历史数据和当前状态，确定最终的交易决策
        # 返回当前交易信号、交易价格、EMA状态、好坏评估等信息
        (current_trade_signal, current_trade_price, current_ema_state,
            good_bad, singleIndex, lastTradeSigle) = process_trade_signal(
            trade_signal_buy, trade_signal_sell, reason_buy, reason_sell,
            slice_df, any_df, state_for_buy, state_for_sell,
            start_idx, count, singleIndex, lastTradeSigle, buysellHistory, df, end_idx
        )

        # 第三步：更新结果列表
        # 将计算得到的各种状态和信号更新到相应的结果列表中，用于后续分析和可视化
        update_result_lists(
            emaStasBuy, emaStasSell, LivemaS_buy, LivemaS_sell,
            emaTrades, state_for_buy, state_for_sell,
            tradeS, current_trade_signal, tradePrice, current_trade_price,
            emaStas, current_ema_state, any_good_bad, good_bad
        )

    
    emaStas = [0] * (count - 1) + emaStas+[0]*(goodBads+1)
    tradeS = [0] * (count - 1) + tradeS+[0]*(goodBads+1)
    tradePrice = [0] * (count - 1) + tradePrice+[0]*(goodBads+1)
    any_good_bad=[0] * (count - 1) + any_good_bad+[0]*(goodBads+1)
    emaStasBuy=[0] * (count - 1) + emaStasBuy+[0]*(goodBads+1)
    emaStasSell=[0] * (count - 1) + emaStasSell+[0]*(goodBads+1)
    emaTrades[0]=[0] * (count - 1) + emaTrades[0]+[0]*(goodBads+1)
    emaTrades[1]=[0] * (count - 1) + emaTrades[1]+[0]*(goodBads+1)
    print(len(emaStas), len(df))

    # print(buysellHistory[0][0])
    # print(buysellHistory[1][0])
    # print(buysellHistory[2][0])
    print(buysellHistory[0])
    # print([x for x in tradeS if x != 0])
    # buysellHistory[0]=[x for x in tradeS if x != 0][:-1]
    # print(buysellHistory[0])
    from Mk_OKX_zyzsBuy import testBuy
    from Mk_OKX_zyzsSell import testSell
    # testBuy(buysellHistory)
    # testSell(buysellHistory)
  
  
    save_json(buysellHistory, 'buysellHistory.json')
    # print(emaStas)
    df['emaStas'] = emaStas
    df['tradeS'] = tradeS
    df['tradePrice'] = tradePrice
    
    df["any_good_bad"]=any_good_bad
    df['emaBuy']=emaStasBuy
    df['emaSell']=emaStasSell
    df['state_for_buy']=emaTrades[0]
    df['state_for_sell']=emaTrades[1]
    
    
    # save_df_to_excel(df,'718哈哈1')
    # from live_tra_juece import advanced_trade_analysis,print_comprehensive_analysis
    #    # 进行高级分析
    # result = advanced_trade_analysis(any_good_bad)
    
    # 打印分析报告
    # print_comprehensive_analysis(any_good_bad)
        # #  ##画图交易
    plot_result = plot_kline_with_trade_signals(df, title='交易信号分析图')
    # plot_result = plot_kline_with_trade_signalsV1(df, plotEmaTrades,title='交易信号分析图')
    # print(df.tail(20))
    

    
    
    
